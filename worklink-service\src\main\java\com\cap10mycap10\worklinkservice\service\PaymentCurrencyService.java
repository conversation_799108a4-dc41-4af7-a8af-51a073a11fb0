package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.model.ExchangeRate;
import com.cap10mycap10.worklinkservice.model.Invoice;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * Service for handling currency conversion in payment workflows
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentCurrencyService {

    private final ExchangeRateService exchangeRateService;

    /**
     * Convert payment amount from hirer's currency to agency's base currency
     */
    public PaymentConversionResult convertPaymentAmount(
            BigDecimal amount, 
            String fromCurrency, 
            String toCurrency) {
        
        try {
            if (fromCurrency.equals(toCurrency)) {
                return new PaymentConversionResult(
                    amount, amount, fromCurrency, toCurrency, BigDecimal.ONE, true
                );
            }

            ExchangeRate exchangeRate = exchangeRateService.getExchangeRateWithFallback(fromCurrency, toCurrency);
            BigDecimal convertedAmount = amount.multiply(exchangeRate.getRate())
                .setScale(2, RoundingMode.HALF_UP);

            return new PaymentConversionResult(
                amount, convertedAmount, fromCurrency, toCurrency, exchangeRate.getRate(), true
            );
        } catch (Exception e) {
            log.error("Failed to convert payment amount from {} to {}: {}", 
                     fromCurrency, toCurrency, e.getMessage());
            
            // Return unconverted amount as fallback
            return new PaymentConversionResult(
                amount, amount, fromCurrency, toCurrency, BigDecimal.ONE, false
            );
        }
    }

    /**
     * Prepare payment details for Stripe with currency conversion
     */
    public StripePaymentDetails prepareStripePayment(
            Invoice invoice, 
            String hirerCurrency, 
            BigDecimal paymentAmount,
            boolean fullPayment) {
        
        String agencyBaseCurrency = invoice.getVehicleBooking().getVehicle().getAgency().getBaseCurrency();
        if (agencyBaseCurrency == null) {
            agencyBaseCurrency = "USD"; // Default fallback
        }

        // Convert payment amount to agency's base currency
        PaymentConversionResult conversion = convertPaymentAmount(
            paymentAmount, hirerCurrency, agencyBaseCurrency
        );

        // Calculate the amount for Stripe (in smallest currency unit)
        long stripeAmount = conversion.getConvertedAmount()
            .multiply(BigDecimal.valueOf(100))
            .longValue();

        return new StripePaymentDetails(
            stripeAmount,
            agencyBaseCurrency.toLowerCase(),
            conversion,
            createPaymentMetadata(invoice, conversion)
        );
    }

    /**
     * Create metadata for payment tracking
     */
    private Map<String, String> createPaymentMetadata(Invoice invoice, PaymentConversionResult conversion) {
        Map<String, String> metadata = new HashMap<>();
        metadata.put("invoice_id", String.valueOf(invoice.getId()));
        metadata.put("booking_id", String.valueOf(invoice.getVehicleBooking().getId()));
        metadata.put("agency_id", String.valueOf(invoice.getVehicleBooking().getVehicle().getAgency().getId()));
        metadata.put("original_amount", conversion.getOriginalAmount().toString());
        metadata.put("original_currency", conversion.getFromCurrency());
        metadata.put("converted_amount", conversion.getConvertedAmount().toString());
        metadata.put("converted_currency", conversion.getToCurrency());
        metadata.put("exchange_rate", conversion.getExchangeRate().toString());
        metadata.put("conversion_successful", String.valueOf(conversion.isConversionSuccessful()));
        return metadata;
    }

    /**
     * Calculate refund amount in original currency
     */
    public PaymentConversionResult calculateRefundAmount(
            BigDecimal refundAmountInAgencyCurrency,
            String agencyCurrency,
            String hirerCurrency,
            BigDecimal originalExchangeRate) {
        
        try {
            if (agencyCurrency.equals(hirerCurrency)) {
                return new PaymentConversionResult(
                    refundAmountInAgencyCurrency, refundAmountInAgencyCurrency, 
                    agencyCurrency, hirerCurrency, BigDecimal.ONE, true
                );
            }

            // For refunds, we typically use the inverse of the original exchange rate
            // to ensure the customer gets back the same amount they paid
            BigDecimal inverseRate = BigDecimal.ONE.divide(originalExchangeRate, 6, RoundingMode.HALF_UP);
            BigDecimal refundInHirerCurrency = refundAmountInAgencyCurrency.multiply(inverseRate)
                .setScale(2, RoundingMode.HALF_UP);

            return new PaymentConversionResult(
                refundAmountInAgencyCurrency, refundInHirerCurrency, 
                agencyCurrency, hirerCurrency, inverseRate, true
            );
        } catch (Exception e) {
            log.error("Failed to calculate refund amount from {} to {}: {}", 
                     agencyCurrency, hirerCurrency, e.getMessage());
            
            return new PaymentConversionResult(
                refundAmountInAgencyCurrency, refundAmountInAgencyCurrency, 
                agencyCurrency, hirerCurrency, BigDecimal.ONE, false
            );
        }
    }

    /**
     * Data class for payment conversion results
     */
    public static class PaymentConversionResult {
        private final BigDecimal originalAmount;
        private final BigDecimal convertedAmount;
        private final String fromCurrency;
        private final String toCurrency;
        private final BigDecimal exchangeRate;
        private final boolean conversionSuccessful;

        public PaymentConversionResult(BigDecimal originalAmount, BigDecimal convertedAmount, 
                                     String fromCurrency, String toCurrency, 
                                     BigDecimal exchangeRate, boolean conversionSuccessful) {
            this.originalAmount = originalAmount;
            this.convertedAmount = convertedAmount;
            this.fromCurrency = fromCurrency;
            this.toCurrency = toCurrency;
            this.exchangeRate = exchangeRate;
            this.conversionSuccessful = conversionSuccessful;
        }

        // Getters
        public BigDecimal getOriginalAmount() { return originalAmount; }
        public BigDecimal getConvertedAmount() { return convertedAmount; }
        public String getFromCurrency() { return fromCurrency; }
        public String getToCurrency() { return toCurrency; }
        public BigDecimal getExchangeRate() { return exchangeRate; }
        public boolean isConversionSuccessful() { return conversionSuccessful; }
    }

    /**
     * Data class for Stripe payment details
     */
    public static class StripePaymentDetails {
        private final long amount;
        private final String currency;
        private final PaymentConversionResult conversion;
        private final Map<String, String> metadata;

        public StripePaymentDetails(long amount, String currency, 
                                  PaymentConversionResult conversion, 
                                  Map<String, String> metadata) {
            this.amount = amount;
            this.currency = currency;
            this.conversion = conversion;
            this.metadata = metadata;
        }

        // Getters
        public long getAmount() { return amount; }
        public String getCurrency() { return currency; }
        public PaymentConversionResult getConversion() { return conversion; }
        public Map<String, String> getMetadata() { return metadata; }
    }
}
