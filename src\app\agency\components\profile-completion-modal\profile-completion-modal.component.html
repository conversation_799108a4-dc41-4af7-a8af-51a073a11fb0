<div class="modal-header border-0">
  <h4 class="modal-title">
    <i class="fas fa-user-cog text-primary me-2"></i>
    Complete Your Agency Profile
  </h4>
</div>

<div class="modal-body">
  <!-- Progress indicator -->
  <div class="mb-4">
    <div class="d-flex justify-content-between align-items-center mb-2">
      <span class="text-muted small">Step {{ step }} of {{ totalSteps }}</span>
      <span class="text-muted small">{{ getProgressPercentage() }}% Complete</span>
    </div>
    <div class="progress" style="height: 4px;">
      <div class="progress-bar bg-primary" 
           [style.width.%]="(step / totalSteps) * 100"
           role="progressbar"></div>
    </div>
  </div>

  <!-- Step content -->
  <div class="text-center mb-4">
    <h5 class="mb-2">{{ getStepTitle() }}</h5>
    <p class="text-muted">{{ getStepDescription() }}</p>
  </div>

  <form [formGroup]="profileForm">
    <!-- Step 1: Currency Selection -->
    <div *ngIf="step === 1">
      <div class="mb-4">
        <h6 class="mb-3">Select Your Base Currency</h6>
        <p class="text-muted small mb-3">
          <i class="fas fa-info-circle"></i>
          This will be used for all vehicle pricing and invoicing. You can change this later in settings.
        </p>
        
        <!-- Current selection display -->
        <div class="alert alert-light border mb-3" *ngIf="profileForm.value.baseCurrency">
          <div class="d-flex align-items-center">
            <i class="fas fa-check-circle text-success me-2"></i>
            <strong>Selected: {{ getCurrentCurrencyInfo().code }}</strong> - {{ getCurrentCurrencyInfo().name }}
            <span class="badge bg-secondary ms-2">{{ getCurrentCurrencyInfo().symbol }}</span>
          </div>
        </div>

        <!-- Currency selection grid -->
        <div class="row g-2">
          <div class="col-6 col-md-4" *ngFor="let currency of commonCurrencies">
            <button 
              type="button" 
              class="btn w-100 text-start"
              [class.btn-primary]="profileForm.value.baseCurrency === currency.code"
              [class.btn-outline-secondary]="profileForm.value.baseCurrency !== currency.code"
              (click)="selectCurrency(currency.code)">
              <div class="d-flex justify-content-between align-items-center">
                <div>
                  <strong>{{ currency.code }}</strong>
                  <br>
                  <small class="text-muted">{{ currency.name }}</small>
                </div>
                <span class="badge bg-light text-dark">{{ currency.symbol }}</span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Contact Information -->
    <div *ngIf="step === 2">
      <div class="mb-4">
        <h6 class="mb-3">Contact Information</h6>
        <p class="text-muted small mb-3">
          <i class="fas fa-info-circle"></i>
          This information is optional but helps clients contact you directly.
        </p>

        <div class="mb-3">
          <label for="telephone" class="form-label">Phone Number</label>
          <input 
            type="tel" 
            class="form-control" 
            id="telephone"
            formControlName="telephone"
            placeholder="Enter your phone number">
        </div>

        <div formGroupName="address">
          <h6 class="mb-2">Address</h6>
          <div class="row g-2">
            <div class="col-12">
              <input 
                type="text" 
                class="form-control" 
                formControlName="street"
                placeholder="Street Address">
            </div>
            <div class="col-6">
              <input 
                type="text" 
                class="form-control" 
                formControlName="city"
                placeholder="City">
            </div>
            <div class="col-6">
              <input 
                type="text" 
                class="form-control" 
                formControlName="state"
                placeholder="State/Province">
            </div>
            <div class="col-6">
              <input 
                type="text" 
                class="form-control" 
                formControlName="postalCode"
                placeholder="Postal Code">
            </div>
            <div class="col-6">
              <input 
                type="text" 
                class="form-control" 
                formControlName="country"
                placeholder="Country">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 3: Logo Upload -->
    <div *ngIf="step === 3">
      <div class="mb-4">
        <h6 class="mb-3">Agency Logo</h6>
        <p class="text-muted small mb-3">
          <i class="fas fa-info-circle"></i>
          Upload your agency logo to personalize your profile. This is optional and can be added later.
        </p>

        <div class="text-center">
          <div class="border rounded p-4 mb-3" style="min-height: 150px;">
            <i class="fas fa-image fa-3x text-muted mb-3"></i>
            <p class="text-muted">Logo upload functionality will be available soon</p>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>

<div class="modal-footer border-0">
  <div class="d-flex justify-content-between w-100">
    <!-- Previous button -->
    <button 
      type="button" 
      class="btn btn-outline-secondary"
      [disabled]="step === 1"
      (click)="previousStep()">
      <i class="fas fa-arrow-left me-1"></i>
      Previous
    </button>

    <div>
      <!-- Skip button (for optional steps) -->
      <button 
        type="button" 
        class="btn btn-outline-secondary me-2"
        *ngIf="step > 1"
        (click)="skipStep()">
        Skip
      </button>

      <!-- Next/Complete button -->
      <button 
        type="button" 
        class="btn btn-primary"
        [disabled]="loading || (step === 1 && !profileForm.value.baseCurrency)"
        (click)="step === totalSteps ? completeProfile() : nextStep()">
        <span *ngIf="loading" class="spinner-border spinner-border-sm me-1"></span>
        <i *ngIf="!loading && step < totalSteps" class="fas fa-arrow-right me-1"></i>
        <i *ngIf="!loading && step === totalSteps" class="fas fa-check me-1"></i>
        {{ step === totalSteps ? 'Complete Setup' : 'Next' }}
      </button>
    </div>
  </div>
</div>
