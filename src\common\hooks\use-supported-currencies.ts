"use client";

import { useState, useEffect, useCallback } from "react";
import { Currency } from "@/common/models";
import { CurrencyService } from "@/common/services/currency.service";

interface SupportedCurrenciesState {
  currencies: Currency[];
  currencyCodes: string[];
  isLoading: boolean;
  error: string | null;
  lastFetched: number | null;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const CACHE_KEY = "supported_currencies_cache";

/**
 * Hook for managing supported currencies with caching
 */
export function useSupportedCurrencies() {
  const [state, setState] = useState<SupportedCurrenciesState>({
    currencies: [],
    currencyCodes: [],
    isLoading: false,
    error: null,
    lastFetched: null,
  });

  const currencyService = new CurrencyService();

  /**
   * Load cached data from localStorage
   */
  const loadFromCache = useCallback((): SupportedCurrenciesState | null => {
    if (typeof window === "undefined") return null;

    try {
      const cached = localStorage.getItem(CACHE_KEY);
      if (!cached) return null;

      const data = JSON.parse(cached) as SupportedCurrenciesState;
      const now = Date.now();

      // Check if cache is still valid
      if (data.lastFetched && (now - data.lastFetched) < CACHE_DURATION) {
        return data;
      }
    } catch (error) {
      console.warn("Failed to load currencies from cache:", error);
    }

    return null;
  }, []);

  /**
   * Save data to cache
   */
  const saveToCache = useCallback((data: SupportedCurrenciesState) => {
    if (typeof window === "undefined") return;

    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn("Failed to save currencies to cache:", error);
    }
  }, []);

  /**
   * Fetch supported currencies from API
   */
  const fetchCurrencies = useCallback(async (force = false) => {
    // Check cache first unless forced
    if (!force) {
      const cached = loadFromCache();
      if (cached) {
        setState(cached);
        return cached.currencies;
      }
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const currencies = await currencyService.getActiveCurrencies();
      const currencyCodes = currencies.map(c => c.code);
      const now = Date.now();

      const newState: SupportedCurrenciesState = {
        currencies,
        currencyCodes,
        isLoading: false,
        error: null,
        lastFetched: now,
      };

      setState(newState);
      saveToCache(newState);
      
      return currencies;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch currencies";
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      throw error;
    }
  }, [currencyService, loadFromCache, saveToCache]);

  /**
   * Check if a currency is supported
   */
  const isCurrencySupported = useCallback((currencyCode: string): boolean => {
    return state.currencyCodes.includes(currencyCode);
  }, [state.currencyCodes]);

  /**
   * Get filtered currencies based on common currencies
   */
  const getFilteredCurrencies = useCallback((commonCurrencies: string[]): Currency[] => {
    return state.currencies.filter(currency => 
      commonCurrencies.includes(currency.code)
    );
  }, [state.currencies]);

  /**
   * Get currency by code
   */
  const getCurrencyByCode = useCallback((code: string): Currency | undefined => {
    return state.currencies.find(currency => currency.code === code);
  }, [state.currencies]);

  // Load currencies on mount
  useEffect(() => {
    fetchCurrencies();
  }, [fetchCurrencies]);

  return {
    currencies: state.currencies,
    currencyCodes: state.currencyCodes,
    isLoading: state.isLoading,
    error: state.error,
    lastFetched: state.lastFetched,
    fetchCurrencies,
    isCurrencySupported,
    getFilteredCurrencies,
    getCurrencyByCode,
    refreshCurrencies: () => fetchCurrencies(true),
  };
}

/**
 * Hook for getting only common supported currencies
 */
export function useCommonSupportedCurrencies() {
  const { currencies, isLoading, error, getFilteredCurrencies } = useSupportedCurrencies();

  // Common currencies in order of preference
  const commonCurrencyCodes = [
    "USD", "EUR", "GBP", "CAD", "AUD", "JPY", "CHF", "CNY", "SEK", "NOK",
    "DKK", "PLN", "CZK", "HUF", "RON", "BGN", "HKD", "SGD", "KRW", "TWD",
    "MYR", "THB", "IDR", "PHP", "VN", "INR", "MXN", "BRL", "ARS", "CLP",
    "COP", "PEN", "AED", "SAR", "ILS", "TRY", "ZAR", "EGP", "NZD"
  ];

  const commonCurrencies = getFilteredCurrencies(commonCurrencyCodes);

  return {
    currencies: commonCurrencies,
    isLoading,
    error,
  };
}

/**
 * Simple hook for checking if currencies are supported
 */
export function useCurrencyValidator() {
  const { currencyCodes, isLoading } = useSupportedCurrencies();

  const validateCurrency = useCallback((currencyCode: string): boolean => {
    if (isLoading) return true; // Allow during loading
    return currencyCodes.includes(currencyCode);
  }, [currencyCodes, isLoading]);

  const validateCurrencies = useCallback((currencyCodes: string[]): string[] => {
    if (isLoading) return currencyCodes; // Allow during loading
    return currencyCodes.filter(code => validateCurrency(code));
  }, [validateCurrency, isLoading]);

  return {
    validateCurrency,
    validateCurrencies,
    isLoading,
  };
}

/**
 * Hook that provides currency info with support validation
 */
export function useCurrencyInfo(currencyCode?: string) {
  const { getCurrencyByCode, isCurrencySupported, isLoading } = useSupportedCurrencies();

  const currencyInfo = currencyCode ? getCurrencyByCode(currencyCode) : undefined;
  const isSupported = currencyCode ? isCurrencySupported(currencyCode) : false;

  return {
    currency: currencyInfo,
    isSupported,
    isLoading,
  };
}
