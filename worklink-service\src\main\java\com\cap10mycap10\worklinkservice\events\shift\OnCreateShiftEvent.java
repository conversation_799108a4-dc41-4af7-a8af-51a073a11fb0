package com.cap10mycap10.worklinkservice.events.shift;

import com.cap10mycap10.worklinkservice.model.Shift;
import org.springframework.context.ApplicationEvent;

import java.util.List;

public class OnCreateShiftEvent extends ApplicationEvent {

    private final Shift shift;
    private final List<String> agencyEmailAddressList;
    private final List<String> workerEmailAddressList;


    public OnCreateShiftEvent(Object source, Shift shift, List<String> agencyEmailAddressList, List<String> workerEmailAddressList) {
        super(source);
        this.shift = shift;
        this.agencyEmailAddressList = agencyEmailAddressList;
        this.workerEmailAddressList = workerEmailAddressList;

    }


    public Shift getShift() {
        return shift;
    }

    public List<String> getAgencyEmailAddressList() {
        return agencyEmailAddressList;
    }

    public List<String> getWorkerEmailAddressList() {
        return workerEmailAddressList;
    }


}
