import { Component, inject, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { environment } from 'src/environments/environment';
import { AgencyService } from 'src/app/shared/services/agency.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { ProfileCompletionModalComponent } from '../components/profile-completion-modal/profile-completion-modal.component';

@Component({
  selector: 'app-agency-home',
  templateUrl: environment.isCarRental? './agency-home.component.html': 'worklink-dashboard.component.html',
  styleUrls: ['./agency-home.component.css'],
  standalone: false,
})
export class AgencyHomeComponent implements OnInit {

  constructor(
    private modalService: NgbModal,
    private agencyService: AgencyService,
    private storageService: StorageService
  ) {}

  ngOnInit(): void {
    this.checkProfileCompletion();
  }

  private checkProfileCompletion(): void {
    // Check if profile completion has been dismissed for this session
    const profileCompleted = localStorage.getItem('profileCompleted');
    if (profileCompleted === 'true') {
      return;
    }

    // Get agency details to check if base currency is set
    const agencyId = this.storageService.decrypt(localStorage.getItem('agentId'));
    if (agencyId) {
      this.agencyService.getAgencyById(agencyId).subscribe(
        (agency) => {
          // If base currency is not set, show profile completion modal
          if (!agency.baseCurrency) {
            this.showProfileCompletionModal();
          } else {
            // Mark as completed if currency is already set
            localStorage.setItem('profileCompleted', 'true');
          }
        },
        (error) => {
          console.error('Error checking agency profile:', error);
        }
      );
    }
  }

  private showProfileCompletionModal(): void {
    const modalRef = this.modalService.open(ProfileCompletionModalComponent, {
      backdrop: 'static',
      keyboard: false,
      size: 'lg',
      centered: true
    });

    modalRef.result.then(
      (result) => {
        if (result === 'completed') {
          // Profile was completed successfully
          console.log('Profile completion successful');
        }
      },
      (dismissed) => {
        // Modal was dismissed - don't show again this session
        localStorage.setItem('profileCompleted', 'true');
      }
    );
  }
}
