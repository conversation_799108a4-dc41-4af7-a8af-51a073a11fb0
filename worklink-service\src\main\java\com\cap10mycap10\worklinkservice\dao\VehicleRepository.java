package com.cap10mycap10.worklinkservice.dao;


import com.cap10mycap10.worklinkservice.enums.AssetStatus;
import com.cap10mycap10.worklinkservice.enums.AssetType;
import com.cap10mycap10.worklinkservice.enums.PromotionType;
import com.cap10mycap10.worklinkservice.enums.TransmissionType;
import com.cap10mycap10.worklinkservice.enums.VehicleType;
import com.cap10mycap10.worklinkservice.model.Agency;
import com.cap10mycap10.worklinkservice.model.Rating;
import com.cap10mycap10.worklinkservice.model.Vehicle;
import org.apache.commons.lang3.Streams;
import org.apache.tomcat.jni.Local;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

@Repository
public interface VehicleRepository extends JpaRepository<Vehicle, Long>, JpaSpecificationExecutor<Vehicle> {

    long countByStatusIn(List<AssetStatus> statuses);

    @Query("SELECT DISTINCT v FROM Vehicle v " +
            "LEFT JOIN v.vehicleLocations vl " +
            "LEFT JOIN vl.location l " +
            "WHERE (:agencyId IS NULL OR v.agency.id = :agencyId) " +
            "AND (:status IS NULL OR v.status = :status) " +
            "AND (:searchQuery IS NULL OR " +
            "     LOWER(v.regno) LIKE LOWER(CONCAT('%',:searchQuery,'%')) OR " +
            "     LOWER(v.model) LIKE LOWER(CONCAT('%',:searchQuery,'%')) OR " +
            "     LOWER(v.name) LIKE LOWER(CONCAT('%',:searchQuery,'%'))) " +
            "AND (:locationId IS NULL OR (l.id = :locationId AND vl.active = true))")
    Page<Vehicle> findAllByAgency(@Param("agencyId") Long agencyId,
                                  @Param("status") AssetStatus status,
                                  @Param("searchQuery") String searchQuery,
                                  @Param("locationId") Long locationId,
                                  Pageable pageable);




    @Query("SELECT DISTINCT v FROM Vehicle v JOIN v.vehicleRates vr " +
            "LEFT JOIN v.promotions p " +
            "LEFT JOIN v.vehicleLocations vl " +
            "LEFT JOIN vl.location loc " +
            "LEFT JOIN VehicleAvailability va on v = va.vehicle AND va.date  BETWEEN :startDate AND :endDate "+
            "WHERE (:vehicleType IS NULL OR v.type = :vehicleType) " +
            "AND (:locationId IS NULL OR (loc.id = :locationId AND vl.active = true)) " +
            "AND ((:agencyId IS NULL AND v.agency.status = 'ACTIVE') OR v.agency.id = :agencyId) " +
            "AND (:transmission IS NULL OR v.transmissionType = :transmission) " +
            "AND (:minPrice IS NULL OR vr.rate >= :minPrice) " +
            "AND (:maxPrice IS NULL OR vr.rate <= :maxPrice) " +
            "AND (:maxDeposit IS NULL OR v.depositAmt <= :maxDeposit) " +
            // Check for vehicle availability
            "AND ( va.vehicle is NULL ) " +
            // Check for vehicle availability ends

            "AND (:lowMileageLimit IS NULL OR v.maxDailyMileage >= :lowMileageLimit OR v.maxDailyMileage =0  OR v.maxDailyMileage IS NULL) " +
            "AND ( :agencyId <> NULL OR v.status = 'AVAILABLE' ) " +
            "AND (:searchQuery IS NULL OR LOWER(v.model) LIKE LOWER(CONCAT('%', :searchQuery, '%')) OR LOWER(v.name) LIKE LOWER(CONCAT('%', :searchQuery, '%'))) " +
            "AND (:startDateTime IS NULL OR :endDateTime IS NULL OR v.id NOT IN " +
            "(  SELECT vb.vehicle.id FROM VehicleBooking vb WHERE " +
            "    (" +
            "      (vb.start <= :endDateTime AND vb.end >= :startDateTime)" +
            "      AND (vb.status in ('BOOKED', 'RESERVED', 'WAITINGAUTH'))" +
            "    )" +
            ")) " +
            "AND (:hasPromotion IS NULL OR (:hasPromotion = true AND EXISTS (SELECT 1 FROM v.promotions promo WHERE promo.status = 'ACTIVE' " +
            "     AND (:promotionType IS NULL OR promo.promotionType = :promotionType) " +
            "     AND (promo.startDate IS NULL OR promo.startDate <= :searchStartDateTime) " +
            "     AND (promo.expiryDate IS NULL OR promo.expiryDate >= :searchEndDateTime))) " +
            "     OR (:hasPromotion = false AND NOT EXISTS (SELECT 1 FROM v.promotions))) " +
            // Ensure the days between startDateTime and endDateTime are greater than minHireDays
            "AND (:startDateTime IS NULL OR :endDateTime IS NULL OR v.minHireDays IS NULL OR FUNCTION('TIMESTAMPDIFF', DAY, :startDateTime, :endDateTime) >= v.minHireDays) " +

            "ORDER BY " +
            "CASE WHEN :sortBy = 'random' THEN FUNCTION('RAND') " +
            "     WHEN :sortBy = 'minDailyRate' AND :sortDirection = 'ASC' THEN (SELECT MIN(vr2.rate) FROM VehicleRate vr2 WHERE vr2.vehicle = v) " +
            "     WHEN :sortBy = 'avgRating' AND :sortDirection = 'ASC' THEN (SELECT COALESCE(AVG(CAST(ri.rate AS float)), 0) FROM Rating r JOIN r.ratingItems ri WHERE r.vehicleBooking.vehicle = v AND r.type = 'VEHICLE') " +
            "     WHEN :sortBy = 'name' AND :sortDirection = 'ASC' THEN v.name " +
            "     WHEN :sortBy = 'model' AND :sortDirection = 'ASC' THEN v.model " +
            "     ELSE NULL END ASC, " +
            "CASE WHEN :sortBy = 'minDailyRate' AND :sortDirection = 'DESC' THEN (SELECT MIN(vr2.rate) FROM VehicleRate vr2 WHERE vr2.vehicle = v) " +
            "     WHEN :sortBy = 'avgRating' AND :sortDirection = 'DESC' THEN (SELECT COALESCE(AVG(CAST(ri.rate AS float)), 0) FROM Rating r JOIN r.ratingItems ri WHERE r.vehicleBooking.vehicle = v AND r.type = 'VEHICLE') " +
            "     WHEN :sortBy = 'name' AND :sortDirection = 'DESC' THEN v.name " +
            "     WHEN :sortBy = 'model' AND :sortDirection = 'DESC' THEN v.model " +
            "     ELSE NULL END DESC")
    Page<Vehicle> findPublicVehicles(
                                        @Param("vehicleType") VehicleType vehicleType,

                                     // 1hrs adjusted dates
                                     @Param("startDateTime") ZonedDateTime startDateTime,
                                     @Param("endDateTime") ZonedDateTime endDateTime,


                                     @Param("startDate") LocalDate startDate,
                                     @Param("endDate") LocalDate endDate,

                                     // Actual search dates
                                     @Param("searchStartDateTime") ZonedDateTime searchStartDateTime,
                                     @Param("searchEndDateTime") ZonedDateTime searchEndDateTime,

                                     @Param("locationId") Long locationId,
                                     @Param("agencyId") Long agencyId,
                                     @Param("searchQuery") String searchQuery,
                                     @Param("transmission") TransmissionType transmission,
                                     @Param("minPrice") Float minPrice,
                                     @Param("maxPrice") Float maxPrice,
                                     @Param("maxDeposit") Float maxDeposit,
                                     @Param("lowMileageLimit") Float lowMileageLimit,
                                     @Param("hasPromotion") Boolean hasPromotion,
                                     @Param("promotionType") PromotionType promotionType,
                                     @Param("sortBy") String sortBy,
                                     @Param("sortDirection") String sortDirection,
                                     Pageable pageable);



    @Query("SELECT v FROM Vehicle v WHERE (:status IS NULL OR v.status = :status) " +
            "AND  (:agencyId IS NULL OR v.agency.id = :agencyId)")
    Page<Vehicle> findAllByStatus(@Param("status") AssetStatus status,@Param("agencyId") Long agencyId, Pageable pageable);

    Vehicle findByRegno(String regno);

    long countByAgencyId(Long agencyId);

    /**
     * Find vehicles available at multiple locations
     */
    @Query("SELECT DISTINCT v FROM Vehicle v " +
            "JOIN v.vehicleLocations vl " +
            "WHERE vl.location.id IN :locationIds " +
            "AND vl.active = true " +
            "AND (:agencyId IS NULL OR v.agency.id = :agencyId) " +
            "AND (:status IS NULL OR v.status = :status)")
    Page<Vehicle> findByLocationIdsAndAgencyIdAndStatus(@Param("locationIds") List<Long> locationIds,
                                                        @Param("agencyId") Long agencyId,
                                                        @Param("status") AssetStatus status,
                                                        Pageable pageable);

    /**
     * Find vehicles that are available at ALL specified locations
     */
    @Query("SELECT v FROM Vehicle v " +
            "WHERE (:agencyId IS NULL OR v.agency.id = :agencyId) " +
            "AND (:status IS NULL OR v.status = :status) " +
            "AND (SELECT COUNT(DISTINCT vl.location.id) FROM v.vehicleLocations vl " +
            "     WHERE vl.location.id IN :locationIds AND vl.active = true) = :locationCount")
    Page<Vehicle> findByAllLocationIdsAndAgencyIdAndStatus(@Param("locationIds") List<Long> locationIds,
                                                           @Param("locationCount") long locationCount,
                                                           @Param("agencyId") Long agencyId,
                                                           @Param("status") AssetStatus status,
                                                           Pageable pageable);

    /**
     * Count vehicles by location
     */
    @Query("SELECT COUNT(DISTINCT v) FROM Vehicle v " +
            "JOIN v.vehicleLocations vl " +
            "WHERE vl.location.id = :locationId " +
            "AND vl.active = true " +
            "AND (:agencyId IS NULL OR v.agency.id = :agencyId)")
    long countByLocationIdAndAgencyId(@Param("locationId") Long locationId, @Param("agencyId") Long agencyId);
}
