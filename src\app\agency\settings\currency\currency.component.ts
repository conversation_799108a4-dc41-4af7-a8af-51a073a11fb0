import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { AgencyService } from 'src/app/shared/services/agency.service';
import { StorageService } from 'src/app/shared/services/storage.service';
import { CurrencyService, Currency } from 'src/app/shared/services/currency.service';

@Component({
  selector: 'app-currency',
  templateUrl: './currency.component.html',
  styleUrls: ['./currency.component.css']
})
export class CurrencyComponent implements OnInit {

  currencyForm: UntypedFormGroup;
  agencyId: number;
  agency: any;
  loading = true;
  updating = false;
  loadingCurrencies = true;

  // Supported currencies from API
  supportedCurrencies: Currency[] = [];

  // Fallback currencies for quick selection
  fallbackCurrencies = [
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
    { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
    { code: 'SEK', name: 'Swedish Krona', symbol: 'kr' },
    { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr' }
  ];

  constructor(
    private fb: UntypedFormBuilder,
    private toast: ToastrService,
    private agencyService: AgencyService,
    private storageService: StorageService,
    private currencyService: CurrencyService
  ) { }

  ngOnInit(): void {
    this.agencyId = this.storageService.decrypt(localStorage.getItem('agentId'));

    this.currencyForm = this.fb.group({
      baseCurrency: ['', Validators.required]
    });

    this.loadSupportedCurrencies();
    this.getAgency();
  }

  loadSupportedCurrencies(): void {
    this.loadingCurrencies = true;
    this.currencyService.getCommonSupportedCurrencies().subscribe(
      currencies => {
        this.supportedCurrencies = currencies;
        this.loadingCurrencies = false;
      },
      error => {
        console.error('Failed to load supported currencies:', error);
        this.supportedCurrencies = this.fallbackCurrencies;
        this.loadingCurrencies = false;
        this.toast.warning('Using fallback currencies. Some currencies may not be available.');
      }
    );
  }

  getAgency() {
    this.agencyService.getAgencyById(this.agencyId).subscribe(
      data => {
        this.agency = data;
        this.currencyForm.patchValue({
          baseCurrency: data.baseCurrency || 'USD'
        });
        this.loading = false;
      },
      err => {
        this.loading = false;
        if (err.status == 0) {
          this.toast.error('Network Connection Failure');
        } else if (err.error?.message) {
          this.toast.error(err.error.message);
        } else if (err.status == 500) {
          this.toast.error('Internal Server Error');
        }
      }
    );
  }

  updateBaseCurrency() {
    if (this.currencyForm.valid) {
      this.updating = true;
      const baseCurrency = this.currencyForm.value.baseCurrency;
      
      this.agencyService.updateAgencyBaseCurrency(this.agencyId, baseCurrency).subscribe(
        data => {
          this.updating = false;
          this.toast.success('Base currency updated successfully');
          this.agency.baseCurrency = baseCurrency;
        },
        err => {
          this.updating = false;
          if (err.status == 0) {
            this.toast.error('Network Connection Failure');
          } else if (err.error?.message) {
            this.toast.error(err.error.message);
          } else if (err.status == 500) {
            this.toast.error('Internal Server Error');
          } else {
            this.toast.error('Failed to update currency');
          }
        }
      );
    } else {
      this.toast.warning('Please select a valid currency');
    }
  }

  selectCurrency(currencyCode: string) {
    this.currencyForm.patchValue({
      baseCurrency: currencyCode
    });
  }

  getCurrentCurrencyInfo() {
    const currentCode = this.currencyForm.value.baseCurrency || this.agency?.baseCurrency || 'USD';
    return this.supportedCurrencies.find(c => c.code === currentCode) ||
           this.fallbackCurrencies.find(c => c.code === currentCode) ||
           { code: currentCode, name: currentCode, symbol: currentCode };
  }

  getDisplayCurrencies() {
    return this.supportedCurrencies.length > 0 ? this.supportedCurrencies : this.fallbackCurrencies;
  }
}
