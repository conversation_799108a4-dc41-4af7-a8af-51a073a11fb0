package com.cap10mycap10.worklinkservice.dao;


import com.cap10mycap10.worklinkservice.model.Location;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LocationRepository extends JpaRepository<Location, Long> {
    Page<Location> findAllByOrderByCityAsc(Pageable pageable);
    @Query("SELECT l FROM Location l WHERE " +
            ":agencyId IS NULL OR l.id IN (SELECT vl.location.id FROM VehicleLocation vl JOIN vl.vehicle v WHERE v.agency.id = :agencyId AND vl.active = true) AND (" +
            "LOWER(l.city) LIKE LOWER(CONCAT('%', :searchParam, '%')) OR LOWER(l.country) LIKE LOWER(CONCAT('%', :searchParam, '%')) OR LOWER(l.iso2) LIKE LOWER(CONCAT('%', :searchParam, '%')) OR LOWER(l.iso3) LIKE LOWER(CONCAT('%', :searchParam, '%')))")
    List<Location> searchAllColumns(@Param("searchParam") String searchParam, @Param("agencyId") Long agencyId);

    @Query("SELECT l FROM Location l WHERE " +
            ":agencyId IS NULL OR l.id IN (SELECT vl.location.id FROM VehicleLocation vl JOIN vl.vehicle v WHERE v.agency.id = :agencyId AND vl.active = true)")
    List<Location> findAllFiltered(@Param("agencyId") Long agencyId);
}
