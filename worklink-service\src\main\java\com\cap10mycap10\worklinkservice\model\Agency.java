package com.cap10mycap10.worklinkservice.model;


import com.cap10mycap10.worklinkservice.enums.AgencyType;
import com.cap10mycap10.worklinkservice.enums.Status;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import org.hibernate.search.annotations.Field;
import org.hibernate.search.annotations.Indexed;

import javax.persistence.*;
import java.util.HashSet;
import java.util.Set;


@Entity
@Indexed
@AllArgsConstructor
@NoArgsConstructor
@Data
@NamedEntityGraph(
         name = "Agency.service",
        attributeNodes = @NamedAttributeNode("service")
)
@NamedEntityGraph(
        name = "Agency.properties",
        attributeNodes = @NamedAttributeNode("properties")
)
public class Agency extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    @Field
    private String name;
    private Boolean corrupted;

    @Column(nullable = false)
    private Boolean isTrainer = false;

    @Column(nullable = false)
    private Boolean isTransporter = false;

    @Field
    private String telephone;

    @Field
    @Column(unique = true, nullable = false)
    private String email;

    private Address address;
    private BankDetails bankDetails;

    private String logo;

    @Field
    private String billingEmail;


    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private Status status;


    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private AgencyType agencyType;


    private String deputyToken;
    private boolean deputyEnabled;
    private String deputyUrl;
    private String refPrefix;

    @Column(name = "base_currency", length = 3)
    private String baseCurrency; // Will be set during profile completion, ISO 4217 currency code

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "base_currency", referencedColumnName = "code", insertable = false, updatable = false)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Currency currency;

    @ManyToOne
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    private Services service;

    @OneToMany(mappedBy = "agency", fetch = FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @JsonIgnore
    @ToString.Exclude
    Set<AgencyWorkerProperties> properties;

    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.ALL})
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinTable(name = "agency_client",
            joinColumns = @JoinColumn(name = "agency_id"),
            inverseJoinColumns = @JoinColumn(name = "client_id")
    )
    private Set<Client> clients = new HashSet<>();


    @JsonIgnore
    @ManyToMany(fetch = FetchType.LAZY, cascade = {   CascadeType.ALL,})
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JoinTable(name = "agency_training",
            joinColumns = @JoinColumn(name = "agency_id"),
            inverseJoinColumns = @JoinColumn(name = "training_id")
    )
    private Set<Training> trainings = new HashSet<>();






    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @OneToMany(mappedBy = "agency", fetch =  FetchType.LAZY)
    private Set<WorkerTrainingSession> workerTrainingSessions = new HashSet<>();

    @ManyToMany(cascade = {
            CascadeType.PERSIST,
            CascadeType.MERGE
    })
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    @JoinTable(name = "agency_worker",
            joinColumns = @JoinColumn(name = "agency_id"),
            inverseJoinColumns = @JoinColumn(name = "worker_id")
    )
    private Set<Worker> workers = new HashSet<>();

    @JsonIgnore
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @ManyToMany(mappedBy = "agencies", fetch = FetchType.LAZY, cascade = {
            CascadeType.ALL})
    private Set<Shift> shifts = new HashSet<>();


    @ManyToMany(mappedBy = "agencies", fetch =  FetchType.EAGER)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Set<TrainingSession> trainingSessionSet = new HashSet<>();


    @ManyToMany(mappedBy = "agencies", fetch =  FetchType.LAZY)
    @EqualsAndHashCode.Exclude
    @ToString.Exclude
    @JsonIgnore
    private Set<Transport> transportInvites = new HashSet<>();


    public void addWorker(Worker worker) {
        this.workers.add(worker);
        worker.getAgencySet().add(this);
    }

    public Boolean getTransporter() {
        return isTransporter;
    }

    public void setTransporter(Boolean transporter) {
        isTransporter = transporter;
    }

    public void removeWorker(Worker worker) {
        workers.remove(worker);
        worker.getAgencySet().remove(this);
    }

    public String getRefPrefix() {
        return refPrefix;
    }

    public void setRefPrefix(String refPrefix) {
        this.refPrefix = refPrefix;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getBillingEmail() {
        return billingEmail;
    }

    public void setBillingEmail(String billingEmail) {
        this.billingEmail = billingEmail;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Services getService() {
        return service;
    }

    public void setService(Services service) {
        this.service = service;
    }

    public Set<AgencyWorkerProperties> getProperties() {
        return properties;
    }

    public void setProperties(Set<AgencyWorkerProperties> properties) {
        this.properties = properties;
    }

    public Set<Client> getClients() {
        return clients;
    }

    public void setClients(Set<Client> clients) {
        this.clients = clients;
    }

    public Set<Worker> getWorkers() {
        return workers;
    }

    public void setWorkers(Set<Worker> workers) {
        this.workers = workers;
    }

    public Set<Shift> getShifts() {
        return shifts;
    }

    public void setShifts(Set<Shift> shifts) {
        this.shifts = shifts;
    }

    public void addClient(Client client) {
        clients.add(client);
        client.getAgencys().add(this);
    }


    public BankDetails getBankDetails() {
        return bankDetails;
    }

    public void setBankDetails(BankDetails bankDetails) {
        this.bankDetails = bankDetails;
    }


    public Set<WorkerTrainingSession> getWorkerTrainingSessions() {
        return workerTrainingSessions;
    }

    public void setWorkerTrainingSessions(Set<WorkerTrainingSession> workerTrainingSessions) {
        this.workerTrainingSessions = workerTrainingSessions;
    }

    public Set<TrainingSession> getTrainingSessionSet() {
        return trainingSessionSet;
    }

    public void setTrainingSessionSet(Set<TrainingSession> trainingSessionSet) {
        this.trainingSessionSet = trainingSessionSet;
    }

    public Set<Transport> getTransportInvites() {
        return transportInvites;
    }

    public void setTransportInvites(Set<Transport> transportInvites) {
        this.transportInvites = transportInvites;
    }

    public boolean isTransporter() {
        return isTransporter;
    }

    public void setTransporter(boolean transporter) {
        isTransporter = transporter;
    }

    public void removeClient(Client client) {
        clients.remove(client);
        client.getAgencys().remove(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Agency)) return false;
        return id != null && id.equals(((Agency) o).getId());
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }

    public Agency(Long id, String name, String logo){
        this.id = id;
        this.name = name;
        this.logo = logo;
    }
    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Agency{");
        sb.append("id=").append(id);
        sb.append(", city='").append(name).append('\'');
        sb.append(", telephone='").append(telephone).append('\'');
        sb.append(", email='").append(email).append('\'');
        sb.append(", address=").append(address);
        sb.append(", logo='").append(logo).append('\'');
        sb.append(", billingEmail='").append(billingEmail).append('\'');
        sb.append(", status=").append(status);
        sb.append(", service=").append(service);
        sb.append(", workers=").append(workers);
        sb.append('}');
        return sb.toString();
    }

}
