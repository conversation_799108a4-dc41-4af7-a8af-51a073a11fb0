"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { detectSupportedUserCurrency, type CurrencyDetectionResult } from "@/common/lib/currency-detection";
import { useUserCurrency } from "./use-user-preferences";
import { getUserPreferredCurrency, setUserPreferredCurrency } from "@/common/lib/currency-utils";

interface AutoCurrencyDetectionOptions {
  /**
   * Whether to automatically set the detected currency as user preference
   * Default: true
   */
  autoSet?: boolean;
  
  /**
   * Whether to skip detection if user already has a preference set
   * Default: true
   */
  skipIfPreferenceExists?: boolean;
  
  /**
   * Whether to enable IP-based detection (slower but more accurate)
   * Default: true
   */
  enableIpDetection?: boolean;
  
  /**
   * Callback when currency is detected
   */
  onCurrencyDetected?: (result: CurrencyDetectionResult) => void;
  
  /**
   * Callback when currency is auto-set
   */
  onCurrencyAutoSet?: (currency: string) => void;
}

interface AutoCurrencyDetectionState {
  isDetecting: boolean;
  detectionResult: CurrencyDetectionResult | null;
  error: string | null;
  hasDetected: boolean;
}

/**
 * Hook for automatic currency detection based on user location
 * Integrates with the user preferences system
 */
export function useAutoCurrencyDetection(options: AutoCurrencyDetectionOptions = {}) {
  const {
    autoSet = true,
    skipIfPreferenceExists = true,
    enableIpDetection = true,
    onCurrencyDetected,
    onCurrencyAutoSet,
  } = options;

  const { preferredCurrency, updatePreferredCurrency } = useUserCurrency();
  const [state, setState] = useState<AutoCurrencyDetectionState>({
    isDetecting: false,
    detectionResult: null,
    error: null,
    hasDetected: false,
  });

  const detectionAttemptedRef = useRef(false);
  const isMountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  /**
   * Perform currency detection
   */
  const detectCurrency = useCallback(async (): Promise<CurrencyDetectionResult | null> => {
    if (detectionAttemptedRef.current) {
      return state.detectionResult;
    }

    // Skip if user already has a preference and skipIfPreferenceExists is true
    if (skipIfPreferenceExists && preferredCurrency !== "USD") {
      console.log("Skipping currency detection - user preference exists:", preferredCurrency);
      return null;
    }

    detectionAttemptedRef.current = true;

    if (!isMountedRef.current) return null;

    setState(prev => ({
      ...prev,
      isDetecting: true,
      error: null,
    }));

    try {
      const result = await detectSupportedUserCurrency();
      
      if (!isMountedRef.current) return null;

      setState(prev => ({
        ...prev,
        isDetecting: false,
        detectionResult: result,
        hasDetected: true,
      }));

      // Call detection callback
      onCurrencyDetected?.(result);

      // Auto-set currency if enabled and different from current preference
      if (autoSet && result.currency !== preferredCurrency) {
        try {
          await updatePreferredCurrency(result.currency);
          onCurrencyAutoSet?.(result.currency);
          console.log(`Auto-set currency to ${result.currency} (detected via ${result.method})`);
        } catch (error) {
          console.warn("Failed to auto-set detected currency:", error);
        }
      }

      return result;
    } catch (error) {
      if (!isMountedRef.current) return null;

      const errorMessage = error instanceof Error ? error.message : "Currency detection failed";
      setState(prev => ({
        ...prev,
        isDetecting: false,
        error: errorMessage,
        hasDetected: true,
      }));

      console.warn("Currency detection failed:", error);
      return null;
    }
  }, [
    preferredCurrency,
    skipIfPreferenceExists,
    autoSet,
    updatePreferredCurrency,
    onCurrencyDetected,
    onCurrencyAutoSet,
    state.detectionResult,
  ]);

  /**
   * Manually trigger currency detection
   */
  const triggerDetection = useCallback(async () => {
    detectionAttemptedRef.current = false;
    return await detectCurrency();
  }, [detectCurrency]);

  /**
   * Reset detection state
   */
  const resetDetection = useCallback(() => {
    detectionAttemptedRef.current = false;
    setState({
      isDetecting: false,
      detectionResult: null,
      error: null,
      hasDetected: false,
    });
  }, []);

  // Auto-detect on mount
  useEffect(() => {
    if (!state.hasDetected && !state.isDetecting) {
      detectCurrency();
    }
  }, [detectCurrency, state.hasDetected, state.isDetecting]);

  return {
    ...state,
    detectCurrency: triggerDetection,
    resetDetection,
    preferredCurrency,
  };
}

/**
 * Simplified hook for basic currency detection without auto-setting
 */
export function useCurrencyDetection() {
  const [result, setResult] = useState<CurrencyDetectionResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const detect = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const detectionResult = await detectSupportedUserCurrency();
      setResult(detectionResult);
      return detectionResult;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Detection failed";
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    result,
    isLoading,
    error,
    detect,
  };
}

/**
 * Hook that provides currency detection info for display purposes
 */
export function useCurrencyDetectionInfo() {
  const { preferredCurrency } = useUserCurrency();
  const { result, isLoading, detect } = useCurrencyDetection();

  const detectionInfo = result ? {
    detectedCurrency: result.currency,
    detectionMethod: result.method,
    confidence: result.confidence,
    country: result.country,
    isUsingDetected: preferredCurrency === result.currency,
  } : null;

  return {
    detectionInfo,
    isDetecting: isLoading,
    detectCurrency: detect,
  };
}
