package com.cap10mycap10.worklinkservice.events.shift;


import com.cap10mycap10.worklinkservice.events.email.EmailService;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;


@Component
public class CreateShiftListener implements ApplicationListener<OnCreateShiftEvent> {

    private final EmailService emailService;


    public CreateShiftListener(final EmailService emailService) {
        this.emailService = emailService;
    }


    @Override
    public void onApplicationEvent(OnCreateShiftEvent onCreateShiftEvent) {
        emailService.sendShiftWithAttachment(
                onCreateShiftEvent.getShift(),
                onCreateShiftEvent.getAgencyEmailAddressList(),
                onCreateShiftEvent.getWorkerEmailAddressList());
    }


}
