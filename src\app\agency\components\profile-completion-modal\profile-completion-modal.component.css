.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-header .modal-title {
  font-weight: 600;
}

.progress {
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  transition: width 0.3s ease;
}

.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.alert {
  border-radius: 8px;
}

.form-control {
  border-radius: 6px;
  border: 1px solid #e1e5e9;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.currency-grid .btn {
  min-height: 70px;
  text-align: left;
}

.badge {
  font-size: 0.75em;
}

.text-muted {
  color: #6c757d !important;
}

.modal-footer {
  padding: 1.5rem;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Custom styling for currency selection buttons */
.btn-outline-secondary {
  border-color: #dee2e6;
  color: #495057;
}

.btn-outline-secondary:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
  color: #495057;
}

/* Animation for step transitions */
.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
