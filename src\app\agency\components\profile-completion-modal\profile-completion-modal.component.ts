import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { AgencyService } from 'src/app/shared/services/agency.service';
import { StorageService } from 'src/app/shared/services/storage.service';

@Component({
  selector: 'app-profile-completion-modal',
  templateUrl: './profile-completion-modal.component.html',
  styleUrls: ['./profile-completion-modal.component.css'],
  standalone: false
})
export class ProfileCompletionModalComponent implements OnInit {

  profileForm: UntypedFormGroup;
  agencyId: number;
  loading = false;
  step = 1; // 1: Currency, 2: Contact Details, 3: Logo (optional)
  totalSteps = 3;

  // Common currencies for quick selection
  commonCurrencies = [
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
    { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
    { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
    { code: 'SEK', name: 'Swedish Krona', symbol: 'kr' },
    { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr' }
  ];

  constructor(
    private fb: UntypedFormBuilder,
    private activeModal: NgbActiveModal,
    private toast: ToastrService,
    private agencyService: AgencyService,
    private storageService: StorageService
  ) { }

  ngOnInit(): void {
    this.agencyId = this.storageService.decrypt(localStorage.getItem('agentId'));
    
    this.profileForm = this.fb.group({
      baseCurrency: ['', Validators.required],
      telephone: [''],
      address: this.fb.group({
        street: [''],
        city: [''],
        state: [''],
        postalCode: [''],
        country: ['']
      }),
      logo: ['']
    });
  }

  selectCurrency(currencyCode: string) {
    this.profileForm.patchValue({
      baseCurrency: currencyCode
    });
  }

  getCurrentCurrencyInfo() {
    const currentCode = this.profileForm.value.baseCurrency;
    return this.commonCurrencies.find(c => c.code === currentCode) || 
           { code: currentCode, name: currentCode, symbol: currentCode };
  }

  nextStep() {
    if (this.step === 1) {
      // Validate currency selection
      if (!this.profileForm.value.baseCurrency) {
        this.toast.warning('Please select a base currency');
        return;
      }
    }
    
    if (this.step < this.totalSteps) {
      this.step++;
    }
  }

  previousStep() {
    if (this.step > 1) {
      this.step--;
    }
  }

  skipStep() {
    if (this.step < this.totalSteps) {
      this.step++;
    } else {
      this.completeProfile();
    }
  }

  completeProfile() {
    if (!this.profileForm.value.baseCurrency) {
      this.toast.warning('Base currency is required');
      return;
    }

    this.loading = true;
    
    // Update base currency first (required)
    this.agencyService.updateAgencyBaseCurrency(this.agencyId, this.profileForm.value.baseCurrency).subscribe(
      () => {
        // Update other profile details if provided
        const profileData = {
          telephone: this.profileForm.value.telephone,
          address: this.profileForm.value.address
        };

        // Only update if there's data to update
        if (profileData.telephone || Object.values(profileData.address).some(val => val)) {
          this.updateProfileDetails(profileData);
        } else {
          this.finishSetup();
        }
      },
      err => {
        this.loading = false;
        this.handleError(err);
      }
    );
  }

  private updateProfileDetails(profileData: any) {
    // This would need to be implemented in the agency service
    // For now, just finish the setup
    this.finishSetup();
  }

  private finishSetup() {
    this.loading = false;
    this.toast.success('Profile setup completed successfully!');
    
    // Mark profile as completed in localStorage
    localStorage.setItem('profileCompleted', 'true');
    
    this.activeModal.close('completed');
  }

  private handleError(err: any) {
    if (err.status == 0) {
      this.toast.error('Network Connection Failure');
    } else if (err.error?.message) {
      this.toast.error(err.error.message);
    } else if (err.status == 500) {
      this.toast.error('Internal Server Error');
    } else {
      this.toast.error('Failed to update profile');
    }
  }

  dismiss() {
    this.activeModal.dismiss('dismissed');
  }

  getStepTitle() {
    switch (this.step) {
      case 1: return 'Select Base Currency';
      case 2: return 'Contact Information';
      case 3: return 'Agency Logo';
      default: return 'Complete Profile';
    }
  }

  getStepDescription() {
    switch (this.step) {
      case 1: return 'Choose your agency\'s base currency for vehicle pricing and invoicing.';
      case 2: return 'Add your contact details and address (optional).';
      case 3: return 'Upload your agency logo to personalize your profile (optional).';
      default: return 'Finish setting up your profile.';
    }
  }

  getProgressPercentage(): number {
    return Math.round((this.step / this.totalSteps) * 100);
  }
}
