"use client";

import React, { useState } from "react";
import { Globe, Check, ChevronDown } from "lucide-react";
import { Button } from "@/common/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/common/components/ui/dropdown-menu";
import { useUserCurrency } from "@/common/hooks/use-user-preferences";
import { formatCurrency, getCurrencySymbol } from "@/common/lib/currency-utils";
import { useToast } from "@/common/hooks/use-toast";
import { useCurrencyDetectionInfo } from "@/common/hooks/use-auto-currency-detection";

// Common currencies for the navigation selector
const COMMON_CURRENCIES = [
  { code: "USD", name: "US Dollar", symbol: "$", flag: "🇺🇸" },
  { code: "EUR", name: "Euro", symbol: "€", flag: "🇪🇺" },
  { code: "GBP", name: "British Pound", symbol: "£", flag: "🇬🇧" },
  { code: "CAD", name: "Canadian Dollar", symbol: "C$", flag: "🇨🇦" },
  { code: "AUD", name: "Australian Dollar", symbol: "A$", flag: "🇦🇺" },
  { code: "JPY", name: "Japanese Yen", symbol: "¥", flag: "🇯🇵" },
  { code: "CHF", name: "Swiss Franc", symbol: "CHF", flag: "🇨🇭" },
  { code: "SEK", name: "Swedish Krona", symbol: "kr", flag: "🇸🇪" },
  { code: "NOK", name: "Norwegian Krone", symbol: "kr", flag: "🇳🇴" },
  { code: "DKK", name: "Danish Krone", symbol: "kr", flag: "🇩🇰" },
];

interface CurrencySelectorNavProps {
  className?: string;
  variant?: "compact" | "full";
}

export function CurrencySelectorNav({ 
  className = "", 
  variant = "compact" 
}: CurrencySelectorNavProps) {
  const { preferredCurrency, updatePreferredCurrency } = useUserCurrency();
  const { detectionInfo } = useCurrencyDetectionInfo();
  const { toast } = useToast();
  const [isChanging, setIsChanging] = useState(false);

  const currentCurrency = COMMON_CURRENCIES.find(c => c.code === preferredCurrency) || {
    code: preferredCurrency,
    name: preferredCurrency,
    symbol: getCurrencySymbol(preferredCurrency),
    flag: "🌍"
  };

  const handleCurrencyChange = async (currencyCode: string) => {
    if (currencyCode === preferredCurrency) return;

    setIsChanging(true);
    try {
      await updatePreferredCurrency(currencyCode);
      const newCurrency = COMMON_CURRENCIES.find(c => c.code === currencyCode);
      toast({
        title: "Currency Updated",
        description: `Prices will now be displayed in ${newCurrency?.name || currencyCode}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update currency preference",
        variant: "destructive",
      });
    } finally {
      setIsChanging(false);
    }
  };

  if (variant === "compact") {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`h-8 px-2 text-sm ${className}`}
            disabled={isChanging}
          >
            <Globe className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">{currentCurrency.symbol}</span>
            <span className="sm:hidden">{currentCurrency.code}</span>
            <ChevronDown className="h-3 w-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Currency
          </DropdownMenuLabel>
          {detectionInfo && !detectionInfo.isUsingDetected && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleCurrencyChange(detectionInfo.detectedCurrency)}
                className="text-blue-600"
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <span>🎯</span>
                    <div>
                      <div className="font-medium">
                        {COMMON_CURRENCIES.find(c => c.code === detectionInfo.detectedCurrency)?.name || detectionInfo.detectedCurrency}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Detected from your location
                      </div>
                    </div>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-700 px-1 rounded">
                    {detectionInfo.confidence}%
                  </span>
                </div>
              </DropdownMenuItem>
            </>
          )}
          <DropdownMenuSeparator />
          {COMMON_CURRENCIES.map((currency) => (
            <DropdownMenuItem
              key={currency.code}
              onClick={() => handleCurrencyChange(currency.code)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-2">
                <span>{currency.flag}</span>
                <div>
                  <div className="font-medium">{currency.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {formatCurrency(100, currency.code)} example
                  </div>
                </div>
              </div>
              {preferredCurrency === currency.code && (
                <Check className="h-4 w-4 text-green-600" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Full variant for larger displays
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Globe className="h-4 w-4 text-muted-foreground" />
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-8 px-3"
            disabled={isChanging}
          >
            <span className="flex items-center gap-2">
              <span>{currentCurrency.flag}</span>
              <span>{currentCurrency.code}</span>
              <span className="text-muted-foreground">({currentCurrency.symbol})</span>
            </span>
            <ChevronDown className="h-3 w-3 ml-2" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel>Select Currency</DropdownMenuLabel>
          {detectionInfo && !detectionInfo.isUsingDetected && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleCurrencyChange(detectionInfo.detectedCurrency)}
                className="text-blue-600"
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    <span>🎯</span>
                    <div>
                      <div className="font-medium">
                        {COMMON_CURRENCIES.find(c => c.code === detectionInfo.detectedCurrency)?.name || detectionInfo.detectedCurrency}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Detected from your location ({detectionInfo.confidence}% confidence)
                      </div>
                    </div>
                  </div>
                </div>
              </DropdownMenuItem>
            </>
          )}
          <DropdownMenuSeparator />
          <div className="grid grid-cols-1 gap-1">
            {COMMON_CURRENCIES.map((currency) => (
              <DropdownMenuItem
                key={currency.code}
                onClick={() => handleCurrencyChange(currency.code)}
                className="flex items-center justify-between p-3"
              >
                <div className="flex items-center gap-3">
                  <span className="text-lg">{currency.flag}</span>
                  <div>
                    <div className="font-medium">{currency.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {currency.code} • {formatCurrency(100, currency.code)} example
                    </div>
                  </div>
                </div>
                {preferredCurrency === currency.code && (
                  <Check className="h-4 w-4 text-green-600" />
                )}
              </DropdownMenuItem>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
