package com.cap10mycap10.worklinkservice.service;

import com.cap10mycap10.worklinkservice.model.Currency;
import com.cap10mycap10.worklinkservice.model.ExchangeRate;
import com.cap10mycap10.worklinkservice.repository.ExchangeRateRepository;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.net.RequestOptions;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExchangeRateService {

    private final ExchangeRateRepository exchangeRateRepository;
    private final CurrencyService currencyService;

    @Value("${env.STRIPE_SECRET_KEY}")
    private String stripeSecretKey;

    @PostConstruct
    public void init() {
        Stripe.apiKey = stripeSecretKey;
    }

    /**
     * Get the latest exchange rate between two currencies
     */
    public Optional<ExchangeRate> getLatestExchangeRate(String fromCurrency, String toCurrency) {
        // If same currency, return rate of 1.0
        if (fromCurrency.equals(toCurrency)) {
            return Optional.of(new ExchangeRate(fromCurrency, toCurrency, BigDecimal.ONE, LocalDateTime.now()));
        }

        return exchangeRateRepository.findLatestRate(fromCurrency, toCurrency);
    }

    /**
     * Get exchange rate with fallback to fetch from Stripe if not found or outdated
     */
    public ExchangeRate getExchangeRateWithFallback(String fromCurrency, String toCurrency) {
        // Check for same currency
        if (fromCurrency.equals(toCurrency)) {
            return new ExchangeRate(fromCurrency, toCurrency, BigDecimal.ONE, LocalDateTime.now());
        }

        // Try to get recent rate (within last hour)
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        Optional<ExchangeRate> recentRate = exchangeRateRepository.findLatestRateAfter(
            fromCurrency, toCurrency, oneHourAgo);

        if (recentRate.isPresent()) {
            return recentRate.get();
        }

        // Fetch from Stripe if no recent rate found
        try {
            return fetchAndSaveExchangeRateFromStripe(fromCurrency, toCurrency);
        } catch (Exception e) {
            log.error("Failed to fetch exchange rate from Stripe for {} to {}: {}", 
                     fromCurrency, toCurrency, e.getMessage());
            
            // Fallback to latest available rate
            Optional<ExchangeRate> latestRate = getLatestExchangeRate(fromCurrency, toCurrency);
            if (latestRate.isPresent()) {
                log.warn("Using outdated exchange rate for {} to {}", fromCurrency, toCurrency);
                return latestRate.get();
            }
            
            throw new RuntimeException("No exchange rate available for " + fromCurrency + " to " + toCurrency);
        }
    }

    /**
     * Convert amount between currencies
     */
    public BigDecimal convertCurrency(BigDecimal amount, String fromCurrency, String toCurrency) {
        ExchangeRate rate = getExchangeRateWithFallback(fromCurrency, toCurrency);
        return amount.multiply(rate.getRate());
    }

    /**
     * Fetch exchange rate from Stripe and save it
     */
    @Transactional
    public ExchangeRate fetchAndSaveExchangeRateFromStripe(String fromCurrency, String toCurrency) 
            throws StripeException {
        log.info("Fetching exchange rate from Stripe: {} to {}", fromCurrency, toCurrency);

        // Fetch from Stripe
        RequestOptions params = RequestOptions.getDefault();
        com.stripe.model.ExchangeRate stripeRate = com.stripe.model.ExchangeRate.retrieve(fromCurrency.toLowerCase(), params);
        
        // Get the rate for the target currency
        Map<String, BigDecimal> rates = stripeRate.getRates();
        BigDecimal rate = rates.get(toCurrency.toLowerCase());
        
        if (rate == null) {
            throw new RuntimeException("Exchange rate not available from Stripe for " + 
                                     fromCurrency + " to " + toCurrency);
        }

        // Create and save exchange rate
        ExchangeRate exchangeRate = new ExchangeRate(
            fromCurrency.toUpperCase(),
            toCurrency.toUpperCase(),
            rate,
            LocalDateTime.now()
        );

        return exchangeRateRepository.save(exchangeRate);
    }

    /**
     * Fetch multiple exchange rates for a base currency
     */
    @Async
    public CompletableFuture<List<ExchangeRate>> fetchMultipleRatesFromStripe(
            String baseCurrency, List<String> targetCurrencies) {
        List<ExchangeRate> rates = new ArrayList<>();
        
        try {
            RequestOptions params =  RequestOptions.getDefault();
            com.stripe.model.ExchangeRate stripeRate = com.stripe.model.ExchangeRate.retrieve(baseCurrency.toLowerCase(), params);
            Map<String, BigDecimal> stripeRates = stripeRate.getRates();
            
            LocalDateTime now = LocalDateTime.now();
            
            for (String targetCurrency : targetCurrencies) {
                if (!baseCurrency.equals(targetCurrency)) {
                    BigDecimal rate = stripeRates.get(targetCurrency.toLowerCase());
                    if (rate != null) {
                        ExchangeRate exchangeRate = new ExchangeRate(
                            baseCurrency.toUpperCase(),
                            targetCurrency.toUpperCase(),
                            rate,
                            now
                        );
                        rates.add(exchangeRateRepository.save(exchangeRate));
                    }
                }
            }
        } catch (StripeException e) {
            log.error("Failed to fetch multiple exchange rates from Stripe: {}", e.getMessage());
        }
        
        return CompletableFuture.completedFuture(rates);
    }

    /**
     * Scheduled task to refresh exchange rates every hour
     */
    @Scheduled(fixedRate = 3600000) // Every hour
    @Transactional
    public void refreshExchangeRates() {
        log.info("Starting scheduled exchange rate refresh...");
        
        List<String> activeCurrencies = currencyService.getActiveCurrencies()
            .stream()
            .map(Currency::getCode)
                .collect(Collectors.toList());

        // Refresh rates for major currencies
        List<String> majorCurrencies = Arrays.asList("USD", "EUR", "GBP", "CAD", "AUD", "JPY");
        
        for (String baseCurrency : majorCurrencies) {
            if (activeCurrencies.contains(baseCurrency)) {
                try {
                    fetchMultipleRatesFromStripe(baseCurrency, activeCurrencies);
                    Thread.sleep(1000); // Rate limiting
                } catch (Exception e) {
                    log.error("Failed to refresh rates for {}: {}", baseCurrency, e.getMessage());
                }
            }
        }
        
        log.info("Completed scheduled exchange rate refresh");
    }

    /**
     * Clean up old exchange rates (older than 7 days)
     */
    @Scheduled(cron = "0 0 2 * * ?") // Daily at 2 AM
    @Transactional
    public void cleanupOldRates() {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(7);
        List<ExchangeRate> oldRates = exchangeRateRepository.findRatesOlderThan(cutoffDate);
        
        if (!oldRates.isEmpty()) {
            // Keep the most recent rate for each currency pair
            Map<String, ExchangeRate> latestRates = new HashMap<>();
            
            for (ExchangeRate rate : oldRates) {
                String key = rate.getFromCurrency() + "_" + rate.getToCurrency();
                ExchangeRate existing = latestRates.get(key);
                if (existing == null || rate.getRateDate().isAfter(existing.getRateDate())) {
                    latestRates.put(key, rate);
                }
            }
            
            // Mark old rates as inactive instead of deleting
            for (ExchangeRate rate : oldRates) {
                if (!latestRates.containsValue(rate)) {
                    rate.setIsActive(false);
                    exchangeRateRepository.save(rate);
                }
            }
            
            log.info("Cleaned up {} old exchange rates", oldRates.size() - latestRates.size());
        }
    }

    /**
     * Get conversion details with exchange rate information
     */
    public Map<String, Object> getConversionDetails(
            BigDecimal amount, String fromCurrency, String toCurrency) {
        ExchangeRate exchangeRate = getExchangeRateWithFallback(fromCurrency, toCurrency);
        BigDecimal convertedAmount = amount.multiply(exchangeRate.getRate());
        
        Map<String, Object> details = new HashMap<>();
        details.put("originalAmount", amount);
        details.put("convertedAmount", convertedAmount);
        details.put("fromCurrency", fromCurrency);
        details.put("toCurrency", toCurrency);
        details.put("exchangeRate", exchangeRate.getRate());
        details.put("rateDate", exchangeRate.getRateDate());
        details.put("source", exchangeRate.getSource());
        
        return details;
    }
}
