-------------------------------------------------------------------------------
Test set: com.worklink.formsservice.FormsServiceApplicationTests
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 12.463 s <<< FAILURE! - in com.worklink.formsservice.FormsServiceApplicationTests
contextLoads  Time elapsed: 0.015 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Invocation of init method failed; nested exception is javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
Caused by: javax.persistence.PersistenceException: [PersistenceUnit: default] Unable to build Hibernate SessionFactory; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
Caused by: org.hibernate.exception.JDBCConnectionException: Unable to open JDBC Connection for DDL execution
Caused by: java.sql.SQLInvalidAuthorizationSpecException: 
Could not connect to address=(host=127.0.0.1)(port=3306)(type=master) : (conn=79) Access denied for user 'root'@'**********' (using password: YES)
Current charset is windows-1252. If password has been set using other charset, consider using option 'passwordCharacterEncoding'
Caused by: java.sql.SQLInvalidAuthorizationSpecException: 
(conn=79) Access denied for user 'root'@'**********' (using password: YES)
Current charset is windows-1252. If password has been set using other charset, consider using option 'passwordCharacterEncoding'

