import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

export interface Currency {
  code: string;
  name: string;
  symbol: string;
  active: boolean;
  decimalPlaces: number;
}

export interface ExchangeRate {
  id: number;
  fromCurrency: string;
  toCurrency: string;
  rate: number;
  rateDate: string;
  createdAt: string;
  source: string;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CurrencyService {
  private baseUrl: string = environment.API_url + environment.coreService + '/api/v1';
  
  // Cache for supported currencies
  private supportedCurrenciesSubject = new BehaviorSubject<Currency[]>([]);
  public supportedCurrencies$ = this.supportedCurrenciesSubject.asObservable();
  
  private lastFetched: number | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  constructor(private http: HttpClient) {
    this.loadSupportedCurrencies();
  }

  /**
   * Get all active currencies
   */
  getActiveCurrencies(): Observable<Currency[]> {
    return this.http.get<Currency[]>(`${this.baseUrl}/currencies/active`);
  }

  /**
   * Get all currencies (active and inactive)
   */
  getAllCurrencies(): Observable<Currency[]> {
    return this.http.get<Currency[]>(`${this.baseUrl}/currencies`);
  }

  /**
   * Get a specific currency by code
   */
  getCurrency(code: string): Observable<Currency> {
    return this.http.get<Currency>(`${this.baseUrl}/currencies/${code}`);
  }

  /**
   * Get supported currency codes (just the codes for quick validation)
   */
  getSupportedCurrencyCodes(): Observable<string[]> {
    return this.getActiveCurrencies().pipe(
      map(currencies => currencies.map(currency => currency.code))
    );
  }

  /**
   * Load and cache supported currencies
   */
  private loadSupportedCurrencies(): void {
    // Check if cache is still valid
    if (this.lastFetched && (Date.now() - this.lastFetched) < this.CACHE_DURATION) {
      return;
    }

    this.getActiveCurrencies().pipe(
      tap(currencies => {
        this.supportedCurrenciesSubject.next(currencies);
        this.lastFetched = Date.now();
      }),
      catchError(error => {
        console.error('Failed to load supported currencies:', error);
        // Return fallback currencies
        const fallbackCurrencies: Currency[] = [
          { code: 'USD', name: 'US Dollar', symbol: '$', active: true, decimalPlaces: 2 },
          { code: 'EUR', name: 'Euro', symbol: '€', active: true, decimalPlaces: 2 },
          { code: 'GBP', name: 'British Pound', symbol: '£', active: true, decimalPlaces: 2 },
          { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', active: true, decimalPlaces: 2 },
          { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', active: true, decimalPlaces: 2 },
          { code: 'JPY', name: 'Japanese Yen', symbol: '¥', active: true, decimalPlaces: 0 },
          { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', active: true, decimalPlaces: 2 },
          { code: 'SEK', name: 'Swedish Krona', symbol: 'kr', active: true, decimalPlaces: 2 },
          { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr', active: true, decimalPlaces: 2 },
          { code: 'DKK', name: 'Danish Krone', symbol: 'kr', active: true, decimalPlaces: 2 }
        ];
        this.supportedCurrenciesSubject.next(fallbackCurrencies);
        return of(fallbackCurrencies);
      })
    ).subscribe();
  }

  /**
   * Refresh supported currencies cache
   */
  refreshSupportedCurrencies(): void {
    this.lastFetched = null;
    this.loadSupportedCurrencies();
  }

  /**
   * Get common supported currencies (filtered list)
   */
  getCommonSupportedCurrencies(): Observable<Currency[]> {
    const commonCurrencyCodes = [
      'USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CHF', 'CNY', 'SEK', 'NOK',
      'DKK', 'PLN', 'CZK', 'HUF', 'RON', 'BGN', 'HKD', 'SGD', 'KRW', 'TWD',
      'MYR', 'THB', 'IDR', 'PHP', 'VND', 'INR', 'MXN', 'BRL', 'ARS', 'CLP',
      'COP', 'PEN', 'AED', 'SAR', 'ILS', 'TRY', 'ZAR', 'EGP', 'NZD'
    ];

    return this.supportedCurrencies$.pipe(
      map(currencies => currencies.filter(currency => 
        commonCurrencyCodes.includes(currency.code)
      ))
    );
  }

  /**
   * Check if a currency is supported
   */
  isCurrencySupported(currencyCode: string): Observable<boolean> {
    return this.supportedCurrencies$.pipe(
      map(currencies => currencies.some(currency => currency.code === currencyCode))
    );
  }

  /**
   * Get currency by code from cached currencies
   */
  getCurrencyByCode(code: string): Observable<Currency | undefined> {
    return this.supportedCurrencies$.pipe(
      map(currencies => currencies.find(currency => currency.code === code))
    );
  }

  /**
   * Get exchange rate between two currencies
   */
  getExchangeRate(fromCurrency: string, toCurrency: string): Observable<ExchangeRate> {
    return this.http.get<ExchangeRate>(
      `${this.baseUrl}/exchange-rates/${fromCurrency}/${toCurrency}`
    );
  }

  /**
   * Get multiple exchange rates for a base currency
   */
  getExchangeRates(baseCurrency: string, targetCurrencies: string[]): Observable<ExchangeRate[]> {
    return this.http.post<ExchangeRate[]>(
      `${this.baseUrl}/exchange-rates/batch`,
      {
        baseCurrency,
        targetCurrencies,
      }
    );
  }

  /**
   * Convert amount between currencies
   */
  convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): Observable<{
    originalAmount: number;
    convertedAmount: number;
    exchangeRate: number;
    fromCurrency: string;
    toCurrency: string;
  }> {
    return this.http.post(
      `${this.baseUrl}/exchange-rates/convert`,
      {
        amount,
        fromCurrency,
        toCurrency,
      }
    );
  }

  /**
   * Update agency's base currency
   */
  updateAgencyBaseCurrency(agencyId: number, currencyCode: string): Observable<void> {
    return this.http.put<void>(`${this.baseUrl}/agencies/${agencyId}/base-currency`, {
      baseCurrency: currencyCode,
    });
  }

  /**
   * Refresh exchange rates from Stripe
   */
  refreshExchangeRates(): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/exchange-rates/refresh`, {});
  }

  /**
   * Get historical exchange rates
   */
  getHistoricalRates(
    fromCurrency: string,
    toCurrency: string,
    fromDate: string,
    toDate: string
  ): Observable<ExchangeRate[]> {
    return this.http.get<ExchangeRate[]>(
      `${this.baseUrl}/exchange-rates/historical`,
      {
        params: {
          fromCurrency,
          toCurrency,
          fromDate,
          toDate,
        },
      }
    );
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currencyCode: string, locale: string = 'en-US'): string {
    try {
      return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currencyCode,
      }).format(amount);
    } catch (error) {
      // Fallback formatting
      return `${currencyCode} ${amount.toFixed(2)}`;
    }
  }

  /**
   * Get currency symbol
   */
  getCurrencySymbol(currencyCode: string): string {
    try {
      const formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode,
      });
      const parts = formatter.formatToParts(0);
      const symbolPart = parts.find(part => part.type === 'currency');
      return symbolPart?.value || currencyCode;
    } catch {
      return currencyCode;
    }
  }
}
